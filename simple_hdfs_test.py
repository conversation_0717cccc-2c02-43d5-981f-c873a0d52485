#!/usr/bin/env python3
"""
简单的HDFS连接性测试
使用WebHDFS API测试连接
"""
import logging
import subprocess
import os
from hdfs import InsecureClient

# 配置
NAMENODE_HOST = "*************"
WEBHDFS_PORT = 9000
PRINCIPAL = "hdfs/<EMAIL>"
KEYTAB_FILE = "/etc/security/keytabs/hdfs.keytab"

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def kerberos_auth(logger):
    """Kerberos认证"""
    logger.info("进行Kerberos认证...")
    try:
        cmd = ['kinit', '-kt', KEYTAB_FILE, PRINCIPAL]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("Kerberos认证成功")
            return True
        else:
            logger.error(f"Kerberos认证失败: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Kerberos认证异常: {e}")
        return False

def test_webhdfs_connection(logger):
    """测试WebHDFS连接"""
    logger.info("测试WebHDFS连接...")
    
    try:
        # 创建WebHDFS客户端
        client = InsecureClient(f'https://{NAMENODE_HOST}:{WEBHDFS_PORT}', user='hdfs')
        
        # 测试连接 - 列出根目录
        files = client.list('/')
        
        logger.info("WebHDFS连接成功! 根目录内容:")
        for item in files[:5]:  # 只显示前5个项目
            logger.info(f"  {item}")
        
        return True
        
    except Exception as e:
        logger.error(f"WebHDFS连接失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 50)
    logger.info("简单HDFS连接性测试")
    logger.info("=" * 50)
    
    # 1. Kerberos认证
    if not kerberos_auth(logger):
        logger.error("❌ Kerberos认证失败")
        return False
    
    # 2. 测试WebHDFS连接
    if test_webhdfs_connection(logger):
        logger.info("✅ HDFS连接测试成功!")
        return True
    else:
        logger.error("❌ HDFS连接测试失败")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
