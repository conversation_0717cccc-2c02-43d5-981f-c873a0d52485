#!/usr/bin/env python3
"""
HDFS WebHDFS代理服务器
在服务端A运行，为客户端B提供HTTP代理服务

工作原理:
1. 在服务端A运行此代理服务器
2. 客户端B通过HTTP请求访问代理
3. 代理服务器处理Kerberos认证并转发请求到HDFS

使用方法:
# 在服务端A运行
python3 proxy_server.py

# 在客户端B访问
curl http://*************:8080/hdfs/list/
curl http://*************:8080/hdfs/list/user
"""

import json
import subprocess
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

class HDFSProxyHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.hdfs_user = "developer"
        self.keytab_path = "/home/<USER>/developer.keytab"
        self.base_url = "https://*************:50470/webhdfs/v1"
        super().__init__(*args, **kwargs)
    
    def _ensure_kerberos_auth(self):
        """确保Kerberos认证有效"""
        try:
            # 检查当前票据
            result = subprocess.run(['klist'], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            
            if result.returncode != 0 or "<EMAIL>" not in result.stdout:
                # 重新认证
                cmd = f"kinit -kt {self.keytab_path} {self.hdfs_user}@HADOOP.COM"
                subprocess.run(cmd, shell=True, check=True)
                print("✓ Kerberos认证已更新")
        except Exception as e:
            print(f"❌ Kerberos认证失败: {e}")
            return False
        return True
    
    def _webhdfs_request(self, path, operation):
        """执行WebHDFS请求"""
        if not self._ensure_kerberos_auth():
            return None
        
        url = f"{self.base_url}{path}?op={operation}&user.name={self.hdfs_user}"
        cmd = f"curl -k --negotiate -u : '{url}'"
        
        try:
            result = subprocess.run(cmd, shell=True,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True, timeout=30)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                print(f"❌ WebHDFS请求失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ WebHDFS请求异常: {e}")
            return None
    
    def _list_directory(self, path="/"):
        """列出目录内容"""
        result = self._webhdfs_request(path, "LISTSTATUS")
        if result and "FileStatuses" in result:
            return result["FileStatuses"]["FileStatus"]
        return []
    
    def _scan_directory(self, path="/", max_depth=3, current_depth=0):
        """递归扫描目录"""
        if current_depth > max_depth:
            return None
        
        items = self._list_directory(path)
        if not items:
            return {
                "path": path,
                "directories": [],
                "files": [],
                "stats": {"dirs": 0, "files": 0, "total_size": 0}
            }
        
        result = {
            "path": path,
            "directories": [],
            "files": [],
            "stats": {"dirs": 0, "files": 0, "total_size": 0}
        }
        
        for item in items:
            item_name = item['pathSuffix']
            item_path = path.rstrip('/') + '/' + item_name
            item_type = item['type']
            
            if item_type == 'DIRECTORY':
                result["stats"]["dirs"] += 1
                
                if current_depth < max_depth:
                    subdir_result = self._scan_directory(item_path, max_depth, current_depth + 1)
                    if subdir_result:
                        result["directories"].append(subdir_result)
                        result["stats"]["dirs"] += subdir_result["stats"]["dirs"]
                        result["stats"]["files"] += subdir_result["stats"]["files"]
                        result["stats"]["total_size"] += subdir_result["stats"]["total_size"]
            
            elif item_type == 'FILE':
                result["stats"]["files"] += 1
                file_size = int(item.get('length', 0))
                result["stats"]["total_size"] += file_size
                
                result["files"].append({
                    "name": item_name,
                    "path": item_path,
                    "size": file_size,
                    "owner": item.get('owner', ''),
                    "group": item.get('group', ''),
                    "permission": item.get('permission', ''),
                    "modification_time": item.get('modificationTime', 0),
                    "replication": item.get('replication', 0)
                })
        
        return result
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path_parts = parsed_url.path.strip('/').split('/')
            
            # 设置CORS头
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            if len(path_parts) >= 2 and path_parts[0] == 'hdfs':
                action = path_parts[1]
                hdfs_path = '/' + '/'.join(path_parts[2:]) if len(path_parts) > 2 else '/'
                
                # 解析查询参数
                query_params = parse_qs(parsed_url.query)
                max_depth = int(query_params.get('depth', [3])[0])
                
                if action == 'list':
                    # 简单列表
                    items = self._list_directory(hdfs_path)
                    response = {
                        "path": hdfs_path,
                        "items": items,
                        "count": len(items)
                    }
                
                elif action == 'scan':
                    # 递归扫描
                    response = self._scan_directory(hdfs_path, max_depth)
                
                elif action == 'status':
                    # 服务状态
                    response = {
                        "status": "running",
                        "hdfs_user": self.hdfs_user,
                        "timestamp": time.time()
                    }
                
                else:
                    response = {"error": f"未知操作: {action}"}
            
            else:
                # 帮助信息
                response = {
                    "service": "HDFS WebHDFS代理服务器",
                    "endpoints": {
                        "/hdfs/status": "服务状态",
                        "/hdfs/list[/path]": "列出目录内容",
                        "/hdfs/scan[/path]?depth=N": "递归扫描目录"
                    },
                    "examples": [
                        "GET /hdfs/list/",
                        "GET /hdfs/list/user",
                        "GET /hdfs/scan/user?depth=5"
                    ]
                }
            
            # 发送响应
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
            
        except Exception as e:
            # 错误响应
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            error_response = {
                "error": str(e),
                "path": self.path
            }
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")


def main():
    print("🚀 HDFS WebHDFS代理服务器")
    print("=" * 50)
    
    # 检查环境
    if not os.path.exists("/home/<USER>/developer.keytab"):
        print("❌ keytab文件不存在")
        return
    
    # 初始Kerberos认证
    try:
        cmd = "kinit -kt /home/<USER>/developer.keytab <EMAIL>"
        subprocess.run(cmd, shell=True, check=True)
        print("✓ Kerberos认证成功")
    except Exception as e:
        print(f"❌ Kerberos认证失败: {e}")
        return
    
    # 启动HTTP服务器
    server_address = ('0.0.0.0', 8081)
    httpd = HTTPServer(server_address, HDFSProxyHandler)
    
    print(f"✓ 代理服务器启动成功")
    print(f"监听地址: http://0.0.0.0:8081")
    print(f"客户端访问: http://*************:8081")
    print("-" * 50)
    print("API端点:")
    print("  GET /hdfs/status - 服务状态")
    print("  GET /hdfs/list[/path] - 列出目录")
    print("  GET /hdfs/scan[/path]?depth=N - 扫描目录")
    print("-" * 50)
    print("按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n⚠️ 服务器已停止")
        httpd.shutdown()


if __name__ == "__main__":
    main()
