import requests
from requests_kerberos import HTTPKerberosAuth, REQUIRED

url = "https://192.168.80.23:50470/webhdfs/v1/?op=LISTSTATUS"

try:
    response = requests.get(
        url,
        auth=HTTPKerberosAuth(mutual_authentication=REQUIRED),
        verify="server.pem"  # 使用你刚转换的 PEM 文件
    )
    print("✅ 响应状态码:", response.status_code)
    print("📄 响应内容:", response.text)
except requests.exceptions.SSLError as ssl_err:
    print("❌ SSL 错误:", ssl_err)
except Exception as e:
    print("❌ 请求异常:", e)
 
