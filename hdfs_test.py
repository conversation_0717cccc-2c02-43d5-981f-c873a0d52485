#!/usr/bin/env python3
"""
HDFS连通性测试脚本
简单测试HDFS文件系统的连接状态
"""
import os
import sys
import logging
import subprocess

# HDFS连接配置
NAMENODE_HOST = "*************"
NAMENODE_PORT = 9000

# Kerberos配置
REALM = "HADOOP.COM"
PRINCIPAL = "hdfs/<EMAIL>"
KEYTAB_FILE = "/etc/security/keytabs/hdfs.keytab"
KRB5_CONF = "/etc/krb5.conf"

# 尝试导入hdfs3库
try:
    from hdfs3 import HDFileSystem
    HDFS3_AVAILABLE = True
except ImportError:
    HDFS3_AVAILABLE = False
    print("警告: hdfs3库未安装，将使用命令行方式测试")


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def check_kerberos_auth(logger) -> bool:
    """检查Kerberos认证"""
    logger.info("检查Kerberos认证...")
    
    # 设置环境变量
    os.environ['KRB5_CONFIG'] = KRB5_CONF
    
    # 检查keytab文件
    if not os.path.exists(KEYTAB_FILE):
        logger.error(f"Keytab文件不存在: {KEYTAB_FILE}")
        return False
    
    # 检查krb5.conf文件
    if not os.path.exists(KRB5_CONF):
        logger.error(f"krb5.conf文件不存在: {KRB5_CONF}")
        return False
    
    try:
        # 使用kinit进行认证
        cmd = ['kinit', '-kt', KEYTAB_FILE, PRINCIPAL]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"Kerberos认证成功: {PRINCIPAL}")
            return True
        else:
            logger.error(f"Kerberos认证失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Kerberos认证异常: {e}")
        return False


def test_hdfs_with_python(logger) -> bool:
    """使用Python hdfs3库测试HDFS连接"""
    if not HDFS3_AVAILABLE:
        return False

    logger.info("使用Python hdfs3库测试HDFS连接...")

    try:
        # 创建HDFS客户端
        hdfs_client = HDFileSystem(
            host=NAMENODE_HOST,
            port=NAMENODE_PORT,
            user='hdfs'
        )

        # 测试连接 - 列出根目录
        files = hdfs_client.ls('/', detail=True)

        logger.info("HDFS连接成功! 根目录内容:")
        for item in files[:5]:  # 只显示前5个项目
            item_type = "目录" if item['kind'] == 'directory' else "文件"
            logger.info(f"  {item_type}: {item['name']}")

        hdfs_client.disconnect()
        return True

    except Exception as e:
        logger.error(f"Python HDFS连接失败: {e}")
        return False


def test_network_connectivity(logger) -> bool:
    """测试网络连通性"""
    logger.info(f"测试到 {NAMENODE_HOST}:{NAMENODE_PORT} 的网络连通性...")

    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((NAMENODE_HOST, NAMENODE_PORT))
        sock.close()

        if result == 0:
            logger.info("网络连通性测试通过")
            return True
        else:
            logger.error(f"无法连接到 {NAMENODE_HOST}:{NAMENODE_PORT}")
            return False

    except Exception as e:
        logger.error(f"网络连通性测试异常: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logging()

    logger.info("=" * 50)
    logger.info("HDFS连通性测试开始")
    logger.info("=" * 50)

    logger.info(f"目标HDFS集群: {NAMENODE_HOST}:{NAMENODE_PORT}")
    logger.info(f"Kerberos域: {REALM}")
    logger.info(f"认证主体: {PRINCIPAL}")

    success_count = 0
    total_tests = 3

    # 1. 测试网络连通性
    if test_network_connectivity(logger):
        logger.info("✓ 网络连通性测试通过")
        success_count += 1
    else:
        logger.error("✗ 网络连通性测试失败")

    # 2. 检查Kerberos认证
    if check_kerberos_auth(logger):
        logger.info("✓ Kerberos认证通过")
        success_count += 1
    else:
        logger.error("✗ Kerberos认证失败")

    # 3. 测试HDFS连接（优先使用Python库）
    hdfs_test_passed = False
    if HDFS3_AVAILABLE:
        if test_hdfs_with_python(logger):
            logger.info("✓ Python HDFS连接测试通过")
            hdfs_test_passed = True
            success_count += 1

    if not hdfs_test_passed:
        logger.info("Python HDFS库不可用或连接失败")
        logger.info("建议安装: pip install hdfs3")

    # 输出测试结果
    logger.info("=" * 50)
    logger.info(f"测试完成: {success_count}/{total_tests} 项通过")

    if success_count >= 2:
        logger.info("✅ HDFS连接基本正常!")
        sys.exit(0)
    else:
        logger.error("❌ 连接测试失败，请检查配置")
        sys.exit(1)


if __name__ == '__main__':
    main()

