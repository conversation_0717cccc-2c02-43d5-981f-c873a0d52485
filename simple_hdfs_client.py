#!/usr/bin/env python3
"""
简化版HDFS WebHDFS客户端
适用于客户端B(21.209)连接服务端A(80.23)

使用前请确保:
1. pip install requests requests-kerberos
2. 配置好krb5.conf和keytab文件
3. 网络连通性正常

使用示例:
python3 simple_hdfs_client.py
"""

import requests
import json
import os
import sys
from urllib.parse import quote
from datetime import datetime
import warnings

# 禁用SSL警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

class SimpleHDFSClient:
    def __init__(self):
        # 配置参数 - 根据实际环境修改
        self.namenode_host = "*************"
        self.namenode_port = 50470
        self.username = "developer"
        self.keytab_path = "/opt/developer.keytab"  # 客户端B上的keytab路径
        
        self.base_url = f"https://{self.namenode_host}:{self.namenode_port}/webhdfs/v1"
        
        # 初始化session
        self.session = requests.Session()
        self.session.verify = False  # 禁用SSL验证
        
        # 设置Kerberos认证
        self._setup_auth()
    
    def _setup_auth(self):
        """设置Kerberos认证"""
        try:
            # 导入Kerberos认证模块
            from requests_kerberos import HTTPKerberosAuth, OPTIONAL
            
            # 使用keytab进行kinit
            if os.path.exists(self.keytab_path):
                os.system(f"kinit -kt {self.keytab_path} {self.username}@HADOOP.COM")
                print(f"✓ Kerberos认证成功: {self.username}")
            else:
                print(f"❌ keytab文件不存在: {self.keytab_path}")
                print("请确保keytab文件已复制到客户端B")
                sys.exit(1)
            
            # 设置HTTP Kerberos认证
            self.session.auth = HTTPKerberosAuth(mutual_authentication=OPTIONAL)
            
        except ImportError:
            print("❌ 请安装requests-kerberos: pip install requests-kerberos")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Kerberos认证配置失败: {e}")
            sys.exit(1)
    
    def _request(self, path, operation, **params):
        """发送WebHDFS请求"""
        url = f"{self.base_url}{quote(path, safe='/')}"
        params.update({
            'op': operation,
            'user.name': self.username
        })
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 请求失败 [{path}]: HTTP {response.status_code}")
                print(f"响应: {response.text[:200]}")
                return None
        except Exception as e:
            print(f"❌ 请求异常 [{path}]: {e}")
            return None
    
    def list_directory(self, path="/"):
        """列出目录内容"""
        result = self._request(path, "LISTSTATUS")
        if result and "FileStatuses" in result:
            return result["FileStatuses"]["FileStatus"]
        return []
    
    def get_file_info(self, path):
        """获取文件信息"""
        result = self._request(path, "GETFILESTATUS")
        if result and "FileStatus" in result:
            return result["FileStatus"]
        return None
    
    def scan_directory(self, path="/", max_depth=3, current_depth=0):
        """扫描目录结构"""
        if current_depth > max_depth:
            return None
        
        indent = "  " * current_depth
        print(f"{indent}📁 {path}")
        
        items = self.list_directory(path)
        if not items:
            print(f"{indent}  (空目录或无权限)")
            return None
        
        result = {
            "path": path,
            "directories": [],
            "files": [],
            "stats": {"dirs": 0, "files": 0, "total_size": 0}
        }
        
        for item in items:
            item_name = item['pathSuffix']
            item_path = path.rstrip('/') + '/' + item_name
            item_type = item['type']
            
            if item_type == 'DIRECTORY':
                result["stats"]["dirs"] += 1
                print(f"{indent}  📂 {item_name}/")
                
                if current_depth < max_depth:
                    subdir_result = self.scan_directory(item_path, max_depth, current_depth + 1)
                    if subdir_result:
                        result["directories"].append(subdir_result)
                        # 累加子目录统计
                        result["stats"]["dirs"] += subdir_result["stats"]["dirs"]
                        result["stats"]["files"] += subdir_result["stats"]["files"]
                        result["stats"]["total_size"] += subdir_result["stats"]["total_size"]
            
            elif item_type == 'FILE':
                result["stats"]["files"] += 1
                file_size = int(item.get('length', 0))
                result["stats"]["total_size"] += file_size
                
                size_str = self._format_size(file_size)
                print(f"{indent}  📄 {item_name} ({size_str})")
                
                result["files"].append({
                    "name": item_name,
                    "path": item_path,
                    "size": file_size,
                    "size_formatted": size_str,
                    "owner": item.get('owner', ''),
                    "group": item.get('group', ''),
                    "permission": item.get('permission', ''),
                    "modification_time": item.get('modificationTime', 0)
                })
        
        return result
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"
    
    def print_summary(self, result):
        """打印扫描摘要"""
        print("\n" + "="*60)
        print("📊 HDFS目录扫描摘要")
        print("="*60)
        print(f"扫描路径: {result['path']}")
        print(f"目录总数: {result['stats']['dirs']}")
        print(f"文件总数: {result['stats']['files']}")
        print(f"总大小: {self._format_size(result['stats']['total_size'])}")
        print(f"扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
    
    def save_result(self, result, filename):
        """保存扫描结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✓ 结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def test_connection(self):
        """测试连接"""
        print("🔍 测试HDFS连接...")
        result = self.get_file_info("/")
        if result:
            print("✅ 连接成功!")
            print(f"根目录信息: owner={result.get('owner')}, permission={result.get('permission')}")
            return True
        else:
            print("❌ 连接失败!")
            return False


def main():
    print("🚀 简化版HDFS WebHDFS客户端")
    print("目标服务器: *************:50470")
    print("-" * 50)
    
    # 创建客户端
    client = SimpleHDFSClient()
    
    # 测试连接
    if not client.test_connection():
        print("\n请检查:")
        print("1. 网络连通性: ping *************")
        print("2. 端口连通性: telnet ************* 50470")
        print("3. Kerberos配置: klist")
        print("4. keytab文件: ls -la /opt/developer.keytab")
        sys.exit(1)
    
    print("\n开始扫描HDFS目录结构...")
    print("-" * 50)
    
    try:
        # 扫描根目录
        result = client.scan_directory("/", max_depth=3)
        
        if result:
            # 打印摘要
            client.print_summary(result)
            
            # 保存结果
            output_file = f"hdfs_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            client.save_result(result, output_file)
            
            print("\n✅ 扫描完成!")
        else:
            print("❌ 扫描失败!")
    
    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
    except Exception as e:
        print(f"\n❌ 扫描异常: {e}")


if __name__ == "__main__":
    main()
