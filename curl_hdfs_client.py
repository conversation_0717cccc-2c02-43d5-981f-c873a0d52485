#!/usr/bin/env python3
"""
基于curl的HDFS WebHDFS客户端
使用curl命令处理Kerberos认证的WebHDFS请求

使用前请确保:
1. 系统安装了curl (通常已安装)
2. 配置好krb5.conf和keytab文件
3. curl支持GSS-Negotiate认证

使用示例:
python3 curl_hdfs_client.py
"""

import json
import os
import sys
import subprocess
from urllib.parse import quote
from datetime import datetime

class CurlHDFSClient:
    def __init__(self):
        # 配置参数 - 根据实际环境修改
        self.namenode_ip = "*************"
        self.namenode_host = "localhost"  # 根据配置文件，Kerberos主体使用localhost
        self.namenode_port = 50470
        self.username = "developer"
        self.keytab_path = "/home/<USER>/developer.keytab"  # 客户端B上的keytab路径

        # 使用IP地址但在Host头中指定localhost
        self.base_url = f"https://{self.namenode_ip}:{self.namenode_port}/webhdfs/v1"

        # 设置认证
        self._setup_auth()
    
    def _setup_auth(self):
        """设置Kerberos认证"""
        try:
            # 检查curl是否支持GSS-Negotiate
            result = subprocess.run(['curl', '--version'],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            if 'GSS-API' not in result.stdout and 'Kerberos' not in result.stdout:
                print("⚠️  警告: curl可能不支持Kerberos认证")

            # 检查keytab文件
            if not os.path.exists(self.keytab_path):
                print(f"❌ keytab文件不存在: {self.keytab_path}")
                print("请确保keytab文件已复制到正确位置")
                sys.exit(1)

            # 使用keytab进行kinit
            cmd = f"kinit -kt {self.keytab_path} {self.username}@HADOOP.COM"
            result = subprocess.run(cmd, shell=True,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            if result.returncode == 0:
                print(f"✓ Kerberos认证成功: {self.username}")
            else:
                print(f"❌ Kerberos认证失败: {result.stderr}")
                sys.exit(1)

            # 验证票据
            result = subprocess.run(['klist'],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            if result.returncode == 0:
                print("✓ Kerberos票据验证成功")
            else:
                print("❌ Kerberos票据验证失败")
                sys.exit(1)

            # 尝试设置临时的hosts映射来解决Kerberos主体匹配问题
            self._setup_hosts_mapping()

        except Exception as e:
            print(f"❌ Kerberos认证配置失败: {e}")
            sys.exit(1)

    def _setup_hosts_mapping(self):
        """设置临时的hosts映射"""
        try:
            # 检查是否已经有正确的映射
            with open('/etc/hosts', 'r') as f:
                hosts_content = f.read()

            # 如果没有localhost映射到我们的IP，添加一个临时的别名
            if f"{self.namenode_ip} localhost" not in hosts_content:
                # 使用一个临时的主机名别名
                temp_hostname = "hdfs-localhost"
                if f"{self.namenode_ip} {temp_hostname}" not in hosts_content:
                    # 添加临时映射
                    subprocess.run(['bash', '-c', f'echo "{self.namenode_ip} {temp_hostname}" >> /etc/hosts'],
                                 check=True)
                    print(f"✓ 添加临时hosts映射: {self.namenode_ip} -> {temp_hostname}")
                    self.namenode_host = temp_hostname
                    self.base_url = f"https://{temp_hostname}:{self.namenode_port}/webhdfs/v1"
        except Exception as e:
            print(f"⚠️  无法设置hosts映射: {e}")
            # 继续使用原来的配置
    
    def _curl_request(self, path, operation, **params):
        """使用curl发送WebHDFS请求"""
        # 构建URL
        url = f"{self.base_url}{quote(path, safe='/')}"

        # 构建参数
        params.update({
            'op': operation,
            'user.name': self.username
        })

        # 构建查询字符串
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{url}?{query_string}"

        # 使用--resolve参数来解决Kerberos主体匹配问题
        # 这样curl会使用localhost作为主机名（匹配HTTP/<EMAIL>），
        # 但实际连接到指定的IP地址
        localhost_url = f"https://localhost:{self.namenode_port}/webhdfs/v1{quote(path, safe='/')}"
        localhost_url += f"?{query_string}"

        auth_methods = [
            # 方法1: 使用--resolve参数，这是最佳方案
            [
                'curl', '-s', '-k', '--negotiate', '-u', ':',
                '--resolve', f'localhost:{self.namenode_port}:{self.namenode_ip}',
                localhost_url
            ],
            # 方法2: 备用方案，使用Host头
            [
                'curl', '-s', '-k', '--negotiate', '-u', ':',
                '-H', f'Host: localhost:{self.namenode_port}',
                full_url
            ],
            # 方法3: 原始方法（可能失败，但作为备用）
            [
                'curl', '-s', '-k', '--negotiate', '-u', ':', full_url
            ]
        ]

        # 尝试不同的认证方法
        for i, curl_cmd in enumerate(auth_methods, 1):
            try:
                result = subprocess.run(curl_cmd,
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                      universal_newlines=True, timeout=30)

                if result.returncode == 0:
                    try:
                        json_result = json.loads(result.stdout)
                        if i > 1:  # 如果不是第一种方法成功，记录一下
                            print(f"✓ 认证方法 {i} 成功")
                        return json_result
                    except json.JSONDecodeError as e:
                        # 如果不是JSON，可能是HTML错误页面，继续尝试下一种方法
                        if "401" in result.stdout or "Authentication" in result.stdout:
                            continue
                        print(f"❌ JSON解析失败 [{path}] (方法{i}): {e}")
                        print(f"响应内容: {result.stdout[:200]}...")
                        continue
                else:
                    # 如果curl命令失败，继续尝试下一种方法
                    continue

            except subprocess.TimeoutExpired:
                print(f"❌ 请求超时 [{path}] (方法{i})")
                continue
            except Exception as e:
                print(f"❌ 请求异常 [{path}] (方法{i}): {e}")
                continue

        # 所有方法都失败了
        print(f"❌ 所有认证方法都失败 [{path}]")
        return None
    
    def list_directory(self, path="/"):
        """列出目录内容"""
        result = self._curl_request(path, "LISTSTATUS")
        if result and "FileStatuses" in result:
            return result["FileStatuses"]["FileStatus"]
        return []
    
    def get_file_info(self, path):
        """获取文件信息"""
        result = self._curl_request(path, "GETFILESTATUS")
        if result and "FileStatus" in result:
            return result["FileStatus"]
        return None
    
    def scan_directory(self, path="/", max_depth=3, current_depth=0):
        """扫描目录结构"""
        if current_depth > max_depth:
            return None
        
        indent = "  " * current_depth
        print(f"{indent}📁 {path}")
        
        items = self.list_directory(path)
        if not items:
            print(f"{indent}  (空目录或无权限)")
            return {
                "path": path,
                "directories": [],
                "files": [],
                "stats": {"dirs": 0, "files": 0, "total_size": 0}
            }
        
        result = {
            "path": path,
            "directories": [],
            "files": [],
            "stats": {"dirs": 0, "files": 0, "total_size": 0}
        }
        
        for item in items:
            item_name = item['pathSuffix']
            item_path = path.rstrip('/') + '/' + item_name
            item_type = item['type']
            
            if item_type == 'DIRECTORY':
                result["stats"]["dirs"] += 1
                print(f"{indent}  📂 {item_name}/")
                
                if current_depth < max_depth:
                    subdir_result = self.scan_directory(item_path, max_depth, current_depth + 1)
                    if subdir_result:
                        result["directories"].append(subdir_result)
                        # 累加子目录统计
                        result["stats"]["dirs"] += subdir_result["stats"]["dirs"]
                        result["stats"]["files"] += subdir_result["stats"]["files"]
                        result["stats"]["total_size"] += subdir_result["stats"]["total_size"]
            
            elif item_type == 'FILE':
                result["stats"]["files"] += 1
                file_size = int(item.get('length', 0))
                result["stats"]["total_size"] += file_size
                
                size_str = self._format_size(file_size)
                mod_time = item.get('modificationTime', 0)
                mod_time_str = datetime.fromtimestamp(mod_time/1000).strftime('%Y-%m-%d %H:%M') if mod_time > 0 else 'Unknown'
                
                print(f"{indent}  📄 {item_name} ({size_str}) [{mod_time_str}]")
                
                result["files"].append({
                    "name": item_name,
                    "path": item_path,
                    "size": file_size,
                    "size_formatted": size_str,
                    "owner": item.get('owner', ''),
                    "group": item.get('group', ''),
                    "permission": item.get('permission', ''),
                    "modification_time": mod_time,
                    "modification_time_formatted": mod_time_str,
                    "replication": item.get('replication', 0)
                })
        
        return result
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"
    
    def print_summary(self, result):
        """打印扫描摘要"""
        print("\n" + "="*60)
        print("📊 HDFS目录扫描摘要")
        print("="*60)
        print(f"扫描路径: {result['path']}")
        print(f"目录总数: {result['stats']['dirs']}")
        print(f"文件总数: {result['stats']['files']}")
        print(f"总大小: {self._format_size(result['stats']['total_size'])}")
        print(f"扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
    
    def save_result(self, result, filename):
        """保存扫描结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✓ 结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def test_connection(self):
        """测试连接"""
        print("🔍 测试HDFS连接...")
        result = self.get_file_info("/")
        if result:
            print("✅ 连接成功!")
            print(f"根目录信息: owner={result.get('owner')}, permission={result.get('permission')}")
            return True
        else:
            print("❌ 连接失败!")
            return False


def main():
    print("🚀 基于curl的HDFS WebHDFS客户端")
    print("目标服务器: *************:50470")
    print("-" * 50)
    
    # 创建客户端
    try:
        client = CurlHDFSClient()
    except SystemExit:
        return
    
    # 测试连接
    if not client.test_connection():
        print("\n故障排除建议:")
        print("1. 检查网络连通性: ping *************")
        print("2. 检查端口连通性: telnet ************* 50470")
        print("3. 检查Kerberos票据: klist")
        print("4. 测试curl命令:")
        print("   curl -k --negotiate -u : 'https://*************:50470/webhdfs/v1/?op=LISTSTATUS&user.name=developer'")
        print("5. 检查curl版本: curl --version | grep -i kerberos")
        return
    
    print("\n开始扫描HDFS目录结构...")
    print("-" * 50)
    
    try:
        # 扫描根目录
        result = client.scan_directory("/", max_depth=3)
        
        if result:
            # 打印摘要
            client.print_summary(result)
            
            # 保存结果
            output_file = f"hdfs_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            client.save_result(result, output_file)
            
            print("\n✅ 扫描完成!")
            print(f"详细结果已保存到: {output_file}")
        else:
            print("❌ 扫描失败!")
    
    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
    except Exception as e:
        print(f"\n❌ 扫描异常: {e}")


if __name__ == "__main__":
    main()
